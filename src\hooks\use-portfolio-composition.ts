"use client";

import { useQuery } from "@tanstack/react-query";
import { PortfolioComposition } from "@/utils/db/dashboard-queries";

// Query keys for portfolio composition
export const portfolioCompositionKeys = {
  all: ["portfolio-composition"] as const,
  lists: () => [...portfolioCompositionKeys.all, "list"] as const,
  list: (portfolioIds: string[]) =>
    [...portfolioCompositionKeys.lists(), { portfolioIds }] as const,
};

// API response interface
interface PortfolioCompositionResponse {
  success: boolean;
  data: PortfolioComposition;
  message: string;
}

// Fetch portfolio composition data from API
async function fetchPortfolioComposition(
  portfolioIds: string[]
): Promise<PortfolioComposition> {
  if (portfolioIds.length === 0) {
    throw new Error("Cel puțin un portofoliu trebuie selectat");
  }

  // Use GET for smaller lists, POST for larger ones
  const usePost = portfolioIds.length > 10; // Arbitrary threshold

  let response: Response;

  if (usePost) {
    response = await fetch("/api/dashboard/portfolio-composition", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ portfolioIds }),
    });
  } else {
    const params = new URLSearchParams({
      portfolioIds: portfolioIds.join(","),
    });
    response = await fetch(`/api/dashboard/portfolio-composition?${params}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  }

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(
      errorData.error ||
        "Nu s-au putut încărca datele de compoziție ale portofoliului"
    );
  }

  const result: PortfolioCompositionResponse = await response.json();
  return result.data;
}

// Hook for fetching portfolio composition
export function usePortfolioComposition(portfolioIds: string[]) {
  return useQuery<PortfolioComposition>({
    queryKey: portfolioCompositionKeys.list(portfolioIds),
    queryFn: () => fetchPortfolioComposition(portfolioIds),
    enabled: portfolioIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    refetchOnWindowFocus: false,
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error instanceof Error && error.message.includes("autentificat")) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
  });
}
