"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { usePortfoliosWithMetrics } from "@/hooks/use-portfolios-query";
import { ChevronDown, FolderOpen } from "lucide-react";
import { useEffect, useState } from "react";

interface PortfolioMultiSelectProps {
  selectedPortfolios: string[];
  onSelectionChange: (portfolioIds: string[]) => void;
  className?: string;
}

export function PortfolioMultiSelect({
  selectedPortfolios,
  onSelectionChange,
  className,
}: PortfolioMultiSelectProps) {
  const { data: portfolios, isLoading, error } = usePortfoliosWithMetrics();
  const [isOpen, setIsOpen] = useState(false);

  // Initialize with all portfolios selected when portfolios are loaded
  useEffect(() => {
    if (
      portfolios?.portfolios &&
      portfolios.portfolios.length > 0 &&
      selectedPortfolios.length === 0
    ) {
      const allPortfolioIds = portfolios.portfolios.map((p) => p.id);
      onSelectionChange(allPortfolioIds);
    }
  }, [portfolios?.portfolios, selectedPortfolios.length, onSelectionChange]);

  const handlePortfolioToggle = (
    portfolioId: string,
    event?: React.MouseEvent
  ) => {
    // Prevent the dropdown from closing when clicking on checkboxes
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    const isSelected = selectedPortfolios.includes(portfolioId);

    if (isSelected) {
      // Remove from selection
      const newSelection = selectedPortfolios.filter(
        (id) => id !== portfolioId
      );
      onSelectionChange(newSelection);
    } else {
      // Add to selection
      onSelectionChange([...selectedPortfolios, portfolioId]);
    }
  };

  const handleSelectAll = (event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    if (!portfolios?.portfolios) return;
    const allPortfolioIds = portfolios.portfolios.map((p) => p.id);
    onSelectionChange(allPortfolioIds);
  };

  const handleDeselectAll = (event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    onSelectionChange([]);
  };

  const getDisplayText = () => {
    if (!portfolios?.portfolios || portfolios.portfolios.length === 0) {
      return "Nu există portofolii";
    }

    if (selectedPortfolios.length === 0) {
      return "Selectează portofolii";
    }

    if (selectedPortfolios.length === portfolios.portfolios.length) {
      return "Toate portofoliile";
    }

    if (selectedPortfolios.length === 1) {
      const portfolio = portfolios.portfolios.find(
        (p) => p.id === selectedPortfolios[0]
      );
      return portfolio?.name || "1 portofoliu";
    }

    return `${selectedPortfolios.length} portofolii`;
  };

  if (isLoading) {
    return (
      <div className={className}>
        <Button variant="outline" disabled className="w-full justify-between">
          <span className="flex items-center gap-2">
            <FolderOpen className="h-4 w-4" />
            Se încarcă portofoliile...
          </span>
          <ChevronDown className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  if (error || !portfolios) {
    return (
      <div className={className}>
        <Button variant="outline" disabled className="w-full justify-between">
          <span className="flex items-center gap-2">
            <FolderOpen className="h-4 w-4" />
            Eroare la încărcarea portofoliilor
          </span>
          <ChevronDown className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <div className={className}>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="w-full justify-between">
            <span className="flex items-center gap-2">
              <FolderOpen className="h-4 w-4" />
              {getDisplayText()}
            </span>
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-80" align="start">
          <DropdownMenuLabel className="flex items-center justify-between">
            <span>Selectează Portofolii</span>
            <div className="flex gap-1">
              <Badge variant="secondary" className="text-xs">
                {selectedPortfolios.length}/{portfolios.portfolios.length}
              </Badge>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />

          {/* Select All / Deselect All */}
          <div className="flex gap-1 p-1">
            <Button
              variant="ghost"
              size="sm"
              className="flex-1 h-8 text-xs"
              onClick={handleSelectAll}
              disabled={
                selectedPortfolios.length === portfolios.portfolios.length
              }
            >
              Selectează Tot
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="flex-1 h-8 text-xs"
              onClick={handleDeselectAll}
              disabled={selectedPortfolios.length === 0}
            >
              Deselectează Tot
            </Button>
          </div>

          <DropdownMenuSeparator />

          {/* Portfolio List */}
          <div className="max-h-60 overflow-y-auto">
            {portfolios.portfolios.map((portfolio) => (
              <div
                key={portfolio.id}
                className="flex items-center space-x-2 px-2 py-2 hover:bg-accent hover:text-accent-foreground cursor-pointer rounded-sm"
                onClick={(e) => handlePortfolioToggle(portfolio.id, e)}
              >
                <Checkbox
                  id={`portfolio-${portfolio.id}`}
                  checked={selectedPortfolios.includes(portfolio.id)}
                  onCheckedChange={(checked) => {
                    // Handle the checkbox change without closing dropdown
                    if (checked) {
                      if (!selectedPortfolios.includes(portfolio.id)) {
                        onSelectionChange([
                          ...selectedPortfolios,
                          portfolio.id,
                        ]);
                      }
                    } else {
                      onSelectionChange(
                        selectedPortfolios.filter((id) => id !== portfolio.id)
                      );
                    }
                  }}
                  onClick={(e) => e.stopPropagation()}
                />
                <label
                  htmlFor={`portfolio-${portfolio.id}`}
                  className="flex flex-col gap-1 min-w-0 flex-1 cursor-pointer"
                  onClick={(e) => e.preventDefault()}
                >
                  <span className="font-medium truncate">{portfolio.name}</span>
                  {portfolio.description && (
                    <span className="text-xs text-muted-foreground truncate">
                      {portfolio.description}
                    </span>
                  )}
                </label>
              </div>
            ))}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
