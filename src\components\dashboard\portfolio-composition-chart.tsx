"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { usePortfolioComposition } from "@/hooks/use-portfolio-composition";
import { AvailableChartColors, getColorClassName } from "@/lib/chartUtils";
import { PortfolioCompositionItem } from "@/utils/db/dashboard-queries";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { DonutChart } from "../ui/donut-chart";

interface PortfolioCompositionChartProps {
  selectedPortfolios: string[];
}

type ViewType =
  | "sector"
  | "industry"
  | "currency"
  | "country"
  | "assetType"
  | "positions";

const viewLabels: Record<ViewType, string> = {
  sector: "Sectoare",
  industry: "Industrii",
  currency: "Monede",
  country: "Țări",
  assetType: "Active",
  positions: "Po<PERSON><PERSON><PERSON>",
};

const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat("ro-RO", {
    style: "currency",
    currency: "EUR",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

const formatPercentage = (value: number): string => {
  return `${value.toFixed(1)}%`;
};

export function PortfolioCompositionChart({
  selectedPortfolios,
}: PortfolioCompositionChartProps) {
  const [activeView, setActiveView] = useState<ViewType>("sector");
  const {
    data: composition,
    isLoading,
    error,
  } = usePortfolioComposition(selectedPortfolios);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Alocări</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              Se încarcă datele...
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Alocări</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <p>Eroare la încărcarea datelor</p>
              <p className="text-sm mt-1">Vă rugăm să încercați din nou</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!composition || composition.totalValue === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Alocări</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <p>Nu există date disponibile</p>
              <p className="text-sm mt-1">Selectați portofolii cu tranzacții</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getCurrentData = (): PortfolioCompositionItem[] => {
    switch (activeView) {
      case "sector":
        return composition.sector;
      case "industry":
        return composition.industry;
      case "currency":
        return composition.currency;
      case "country":
        return composition.country;
      case "assetType":
        return composition.assetType;
      case "positions":
        return composition.positions.slice(0, 10); // Show top 10 positions
      default:
        return [];
    }
  };

  const currentData = getCurrentData();

  // Prepare data for local DonutChart component
  const chartData = currentData.map((item) => ({
    name: item.name,
    value: item.value,
    percentage: item.percentage,
    ticker: item.ticker, // Include ticker for positions view
  }));

  // Prepare colors array for the chart
  const chartColors = currentData.map(
    (_, index) => AvailableChartColors[index % AvailableChartColors.length]
  );

  // Custom value formatter for the chart
  const valueFormatter = (value: number) => formatCurrency(value);

  return (
    <Card>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle>Alocări</CardTitle>
          <Badge variant="secondary" className="text-xs">
            {formatCurrency(composition.totalValue)}
          </Badge>
        </div>

        {/* View Selector Tabs */}
        <div className="flex flex-wrap gap-1 mt-4">
          {(Object.keys(viewLabels) as ViewType[]).map((view) => (
            <Button
              key={view}
              variant={activeView === view ? "default" : "outline"}
              size="sm"
              className="text-xs h-7"
              onClick={() => setActiveView(view)}
              disabled={view === "sector" && composition.sector.length === 0}
            >
              {viewLabels[view]}
            </Button>
          ))}
        </div>
      </CardHeader>

      <CardContent>
        {currentData.length === 0 ? (
          <div className="h-64 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <p>
                Nu există date pentru {viewLabels[activeView].toLowerCase()}
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Donut Chart */}
            <div className="flex justify-center">
              <DonutChart
                data={chartData}
                category="name"
                value="value"
                valueFormatter={valueFormatter}
                colors={chartColors}
                className="h-48 w-48"
                showLabel={true}
              />
            </div>

            {/* Center Value Display */}
            {/* <div className="text-center -mt-32 relative z-10 pointer-events-none">
              <div className="bg-background/80 backdrop-blur-sm rounded-lg p-2 inline-block">
                <p className="text-xs text-muted-foreground">Total Net Worth</p>
                <p className="text-lg font-semibold text-primary">
                  {formatCurrency(composition.totalValue)}
                </p>
              </div>
            </div> */}

            {/* Legend */}
            <div className="mt-8">
              <div className="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto pe-2">
                {chartData.map((item, index) => (
                  <div
                    key={item.name}
                    className="flex items-center justify-between text-sm"
                  >
                    <div className="flex items-center gap-2 min-w-0 flex-1">
                      <div
                        className={`w-3 h-3 rounded-full flex-shrink-0 ${getColorClassName(
                          chartColors[index],
                          "bg"
                        )}`}
                      />
                      <span
                        className="truncate"
                        title={`${item.name} - ${item.ticker}`}
                      >
                        {item.name}
                        {activeView === "positions" && item.ticker && (
                          <span className="text-muted-foreground ml-1">
                            ({item.ticker})
                          </span>
                        )}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 flex-shrink-0">
                      <span className="font-medium">
                        {formatPercentage(item.percentage)}
                      </span>
                      <span className="text-muted-foreground text-xs">
                        {formatCurrency(item.value)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Show more indicator for positions */}
            {activeView === "positions" &&
              composition.positions.length > 10 && (
                <div className="text-center">
                  <p className="text-xs text-muted-foreground">
                    Se afișează primele 10 poziții din{" "}
                    {composition.positions.length} total
                  </p>
                </div>
              )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
