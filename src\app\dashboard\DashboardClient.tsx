"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { PortfolioMultiSelect } from "@/components/dashboard/portfolio-multi-select";
import { PortfolioCompositionChart } from "@/components/dashboard/portfolio-composition-chart";
import { useState } from "react";

export default function DashboardClient() {
  const [selectedPortfolios, setSelectedPortfolios] = useState<string[]>([]);

  return (
    <div className="container mx-auto px-4 py-8 min-h-screen max-w-7xl">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-50 mb-2">
          Dashboard
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Analiz<PERSON> completă a portofoliului tău de investiții
        </p>
      </div>

      {/* Portfolio Filter */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <div className="flex-shrink-0">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-50">
              Filtrează după portofolii:
            </h3>
          </div>
          <div className="w-full sm:w-80">
            <PortfolioMultiSelect
              selectedPortfolios={selectedPortfolios}
              onSelectionChange={setSelectedPortfolios}
            />
          </div>
        </div>
      </div>

      {/* Dashboard Grid Layout */}
      <div className="grid gap-6">
        {/* Row 1: 2 cards (left wider than right) */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Card 1 - Performance Overview (2/3 width) */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Performanța Portofoliului</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <div className="text-lg font-medium mb-2">
                    Grafic de performanță
                  </div>
                  <div className="text-sm">
                    Evoluția valorii portofoliului în timp
                  </div>
                  <div className="text-xs mt-2 text-orange-500">
                    În dezvoltare
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Card 2 - Portfolio Composition (1/3 width) */}
          <div className="lg:col-span-1">
            <PortfolioCompositionChart
              selectedPortfolios={selectedPortfolios}
            />
          </div>
        </div>

        {/* Row 2: 2 cards (left wider than right) */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Card 3 - Dividends (2/3 width) */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Dividende</span>
                <div className="flex items-center gap-4">
                  <div className="text-center">
                    <div className="text-sm text-muted-foreground">
                      Anul acesta
                    </div>
                    <div className="text-lg font-bold text-blue-600">
                      €456.78
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-muted-foreground">
                      Yield mediu
                    </div>
                    <div className="text-lg font-bold text-purple-600">
                      3.2%
                    </div>
                  </div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <div className="text-lg font-medium mb-2">
                    Istoric dividende
                  </div>
                  <div className="text-sm">
                    Grafic cu dividendele primite pe luni
                  </div>
                  <div className="text-xs mt-2 text-orange-500">
                    În dezvoltare
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Card 4 - Key Metrics (1/3 width) */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle>Metrici Cheie</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">
                    Valoare totală
                  </span>
                  <span className="font-bold">€43,000.21</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">
                    Câștig/Pierdere
                  </span>
                  <span className="font-bold text-green-600">+€1,234.56</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">
                    Randament
                  </span>
                  <span className="font-bold text-green-600">+2.87%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">
                    Nr. poziții
                  </span>
                  <span className="font-bold">12</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">
                    Diversificare
                  </span>
                  <span className="font-bold text-blue-600">Bună</span>
                </div>
                <div className="mt-6 pt-4 border-t">
                  <div className="text-center text-xs text-orange-500">
                    Metrici avansate în dezvoltare
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Row 3: 1 full-width card */}
        <div className="grid grid-cols-1 gap-6">
          {/* Card 5 - Holdings Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Pozițiile Mele</span>
                <div className="text-sm text-muted-foreground">
                  12 poziții active
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Sample table header */}
                <div className="grid grid-cols-6 gap-4 text-sm font-medium text-muted-foreground border-b pb-2">
                  <div>Simbol</div>
                  <div>Nume</div>
                  <div className="text-right">Cantitate</div>
                  <div className="text-right">Preț</div>
                  <div className="text-right">Valoare</div>
                  <div className="text-right">P&L</div>
                </div>

                {/* Sample rows */}
                {[
                  {
                    symbol: "AAPL",
                    name: "Apple Inc.",
                    qty: "10",
                    price: "$150.25",
                    value: "$1,502.50",
                    pl: "+$125.30",
                    plColor: "text-green-600",
                  },
                  {
                    symbol: "MSFT",
                    name: "Microsoft Corp.",
                    qty: "5",
                    price: "$280.15",
                    value: "$1,400.75",
                    pl: "+$89.45",
                    plColor: "text-green-600",
                  },
                  {
                    symbol: "GOOGL",
                    name: "Alphabet Inc.",
                    qty: "3",
                    price: "$2,450.80",
                    value: "$7,352.40",
                    pl: "-$45.20",
                    plColor: "text-red-600",
                  },
                ].map((row, index) => (
                  <div
                    key={index}
                    className="grid grid-cols-6 gap-4 text-sm py-2 border-b border-gray-100 dark:border-gray-800"
                  >
                    <div className="font-medium">{row.symbol}</div>
                    <div className="truncate">{row.name}</div>
                    <div className="text-right">{row.qty}</div>
                    <div className="text-right">{row.price}</div>
                    <div className="text-right font-medium">{row.value}</div>
                    <div className={`text-right font-medium ${row.plColor}`}>
                      {row.pl}
                    </div>
                  </div>
                ))}

                <div className="mt-6 pt-4 border-t text-center">
                  <div className="text-sm text-orange-500">
                    Tabel complet cu toate pozițiile în dezvoltare
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
