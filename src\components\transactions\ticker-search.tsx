/* eslint-disable @next/next/no-img-element */
"use client";

import { Combobox, ComboboxOption } from "@/components/ui/combobox";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { useAssetSearch } from "@/hooks/use-asset-search";
import {
  getBaseSymbol,
  isValidTickerFormat,
  normalizeTicker,
} from "@/lib/ticker-utils";
import { cn } from "@/lib/utils";
import { Database, Globe, TrendingUp } from "lucide-react";
import * as React from "react";
import { toast } from "sonner";

interface TickerSearchProps {
  value?: string;
  onValueChange?: (ticker: string) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: boolean;
  className?: string;
  onAssetCreated?: (asset: any) => void; // Callback when a new asset is created
  onAssetCreationProgress?: (progress: {
    step: string;
    message: string;
    progress: number;
    details?: string;
  }) => void; // Progress callback
  // Asset filtering props
  assets?: any[]; // Pre-filtered assets to display (optional, if not provided will use search)
  allowAssetCreation?: boolean; // Whether to allow asset creation (default: true)
  allowCustomValue?: boolean; // Whether to allow custom value entry (default: true)
}

export function TickerSearch({
  value,
  onValueChange,
  placeholder = "Caută după simbol, nume sau companie...",
  disabled = false,
  error = false,
  className,
  onAssetCreated,
  assets: providedAssets,
  allowAssetCreation = true,
  allowCustomValue = true,
}: TickerSearchProps) {
  // Use provided assets or search for assets
  const shouldUseSearch = !providedAssets;

  // State for search term when using provided assets (no API calls)
  const [localSearchTerm, setLocalSearchTerm] = React.useState("");

  const {
    assets: searchAssets,
    loading,
    error: searchError,
    searchTerm: apiSearchTerm,
    setSearchTerm: setApiSearchTerm,
  } = useAssetSearch({
    debounceMs: 300,
    minSearchLength: shouldUseSearch ? 1 : 999, // Disable search when using provided assets
    maxResults: 15,
    databaseOnly: !allowAssetCreation, // Use database-only when asset creation is disabled
  });

  // Use local search term for provided assets, API search term for search
  const searchTerm = shouldUseSearch ? apiSearchTerm : localSearchTerm;
  const setSearchTerm = shouldUseSearch ? setApiSearchTerm : setLocalSearchTerm;

  // Filter provided assets based on search term
  const filteredProvidedAssets = React.useMemo(() => {
    if (!providedAssets || !searchTerm || searchTerm.length < 1) {
      return providedAssets || [];
    }

    const searchLower = searchTerm.toLowerCase().trim();
    if (!searchLower) {
      return providedAssets;
    }

    return providedAssets.filter((asset) => {
      const ticker = asset.ticker?.toLowerCase() || "";
      const name = asset.name?.toLowerCase() || "";
      const description = asset.description?.toLowerCase() || "";

      return (
        ticker.includes(searchLower) ||
        name.includes(searchLower) ||
        description.includes(searchLower)
      );
    });
  }, [providedAssets, searchTerm]);

  // Use filtered provided assets or search results
  const assets = providedAssets ? filteredProvidedAssets : searchAssets;

  // State for asset creation
  const [isCreatingAsset, setIsCreatingAsset] = React.useState(false);
  const [creationError, setCreationError] = React.useState<string | null>(null);
  const [creationProgress, setCreationProgress] = React.useState<{
    step: string;
    message: string;
    progress: number;
    details?: string;
  } | null>(null);

  // Handle asset creation with fallback retry mechanism
  const handleCreateAsset = async (ticker: string) => {
    const normalizedTicker = normalizeTicker(ticker);

    setIsCreatingAsset(true);
    setCreationError(null);
    setCreationProgress(null);

    // Helper function to attempt asset creation
    const attemptAssetCreation = async (tickerToTry: string) => {
      const response = await fetch("/api/assets/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ticker: tickerToTry,
          options: {
            refresh: true,
            useMock: false,
            daysHistory: 365,
            yearsHistoryDividend: 10,
          },
        }),
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        const error = new Error(result.error || "Nu s-a putut crea activul");
        (error as any).status = response.status;
        throw error;
      }

      return { response, result, tickerUsed: tickerToTry };
    };

    try {
      let finalResult;

      try {
        // First attempt: try with the original ticker (including exchange suffix)
        finalResult = await attemptAssetCreation(normalizedTicker);
      } catch (primaryError: any) {
        // Check if this is a 503 error or external API failure
        const shouldRetry =
          primaryError.status === 503 ||
          (primaryError.message && primaryError.message.includes("503"));

        if (shouldRetry && normalizedTicker.includes(".")) {
          // Extract base symbol and retry
          const baseTicker = getBaseSymbol(normalizedTicker);

          if (baseTicker !== normalizedTicker) {
            try {
              // Second attempt: try with base ticker only
              finalResult = await attemptAssetCreation(baseTicker);

              // Show info toast about fallback
              toast.info(
                `Activul a fost creat cu simbolul de bază "${baseTicker}" în loc de "${normalizedTicker}"`
              );
            } catch (fallbackError) {
              // Both attempts failed
              throw fallbackError;
            }
          } else {
            // No fallback possible, re-throw original error
            throw primaryError;
          }
        } else {
          // Not a 503 error or no exchange suffix, re-throw original error
          throw primaryError;
        }
      }

      // Success - notify parent component and select the new asset
      // toast.success(`Activul ${tickerUsed} a fost adăugat cu succes!`);

      if (onAssetCreated) {
        onAssetCreated(finalResult.result.data.asset);
      }

      // Select the newly created asset (use the ticker that actually worked)
      // here should be cahnged to use the ticker which was saved in the db
      onValueChange?.(finalResult.result.data.asset.ticker);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "A apărut o eroare";
      setCreationError(errorMessage);

      // Show detailed error toast
      toast.error(`Nu s-a putut adăuga activul ${normalizedTicker}`, {
        description: errorMessage,
        duration: 6000,
        action: {
          label: "Încearcă din nou",
          onClick: () => handleCreateAsset(ticker),
        },
      });
    } finally {
      setIsCreatingAsset(false);
      setCreationProgress(null);
    }
  };

  // Convert assets to combobox options
  const options: ComboboxOption[] = React.useMemo(() => {
    // Assets are already filtered at the database level for SELL transactions
    const assetOptions = assets.map((asset) => {
      // For EODHD results, use displayTicker if available, otherwise use ticker
      const displayValue =
        asset.source === "eodhd" && asset.displayTicker
          ? asset.displayTicker
          : asset.ticker;

      // Use displayTicker for unique value to handle multi-exchange stocks
      const uniqueValue =
        asset.source === "eodhd" && asset.displayTicker
          ? asset.displayTicker
          : asset.ticker;

      return {
        value: uniqueValue, // Use the full ticker with exchange for uniqueness
        label: displayValue,
        description: asset.description,
        // Add metadata for rendering
        metadata: {
          source: asset.source,
          isExistingAsset: asset.isExistingAsset,
          exchange: asset.exchange,
          type: asset.type,
          country: asset.country,
          currency: asset.currency,
          baseTicker: asset.ticker, // Store base ticker for backward compatibility
        } as any,
      };
    });

    // If there's a selected value that's not in the current search results,
    // add it as an option to ensure it can be displayed
    if (value && !assetOptions.some((option) => option.value === value)) {
      assetOptions.unshift({
        value: value,
        label: value,
        description: "Simbol introdus manual",
        metadata: {
          source: "manual" as const,
          isExistingAsset: false,
        } as any,
      });
    }

    return assetOptions;
  }, [assets, value]);

  // Handle value change
  const handleValueChange = (ticker: string) => {
    const selectedAsset = assets.find((asset) => {
      const assetValue =
        asset.source === "eodhd" && asset.displayTicker
          ? asset.displayTicker
          : asset.ticker;
      return assetValue === ticker;
    });

    if (selectedAsset) {
      if (selectedAsset.isExistingAsset) {
        // Asset exists in database, proceed normally
        onValueChange?.(ticker.toUpperCase());
      } else if (allowAssetCreation) {
        // Asset doesn't exist in database, trigger creation if allowed
        const tickerToCreate =
          selectedAsset.source === "eodhd" && selectedAsset.displayTicker
            ? selectedAsset.displayTicker
            : ticker;
        handleCreateAsset(tickerToCreate);
        return;
      } else {
        // Asset creation not allowed
        toast.error("Poți selecta doar active existente");
        return;
      }
    } else {
      // Manual entry or unknown ticker
      onValueChange?.(ticker.toUpperCase());
    }
  };

  // Handle search change
  const handleSearchChange = (search: string) => {
    setSearchTerm(search);
    // Clear creation error and progress when user starts typing again
    if (creationError) {
      setCreationError(null);
    }
    if (creationProgress) {
      setCreationProgress(null);
    }
  };

  // Custom option renderer
  const renderOption = (option: ComboboxOption) => {
    // Find asset by matching the unique value (displayTicker or ticker)
    const asset = assets.find((a) => {
      const assetValue =
        a.source === "eodhd" && a.displayTicker ? a.displayTicker : a.ticker;
      return assetValue === option.value;
    });
    if (!asset) return option.label;

    // Choose icon based on source and status
    const getAssetIcon = () => {
      if (asset.source === "database") {
        return <Database className="h-3 w-3 text-blue-600" />;
      } else if (asset.source === "eodhd") {
        return asset.isExistingAsset ? (
          <Database className="h-3 w-3 text-blue-600" />
        ) : (
          <Globe className="h-3 w-3 text-orange-600" />
        );
      }
      return <TrendingUp className="h-3 w-3 text-muted-foreground" />;
    };

    // Choose background color based on source and status
    const getIconBgClass = () => {
      if (asset.source === "database") {
        return "bg-blue-100 dark:bg-blue-900/20";
      } else if (asset.source === "eodhd") {
        return asset.isExistingAsset
          ? "bg-blue-100 dark:bg-blue-900/20"
          : "bg-orange-100 dark:bg-orange-900/20";
      }
      return "bg-muted";
    };

    return (
      <div className="flex items-center space-x-3">
        {/* Asset Logo or Icon */}
        <div
          className={cn(
            "flex h-6 w-6 items-center justify-center rounded-sm",
            getIconBgClass()
          )}
        >
          {asset.source === "database" && asset.logo_url ? (
            <img
              src={asset.logo_url}
              alt={`${asset.company} logo`}
              className="h-6 w-6 rounded-sm object-contain"
              onError={(e) => {
                // Fallback to icon if image fails to load
                const target = e.target as HTMLImageElement;
                target.style.display = "none";
                target.nextElementSibling?.classList.remove("hidden");
              }}
            />
          ) : null}
          <div
            className={cn(
              asset.source === "database" && asset.logo_url ? "hidden" : ""
            )}
          >
            {getAssetIcon()}
          </div>
        </div>

        {/* Asset Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            {/* Primary ticker display with exchange highlighting */}
            <div className="flex items-center space-x-1">
              <span className="font-medium text-foreground group-hover:text-accent-foreground">
                {asset.source === "eodhd" && asset.displayTicker ? (
                  <>
                    <span>{asset.ticker}</span>
                    <span className="text-blue-600 dark:text-blue-400">
                      .{asset.exchange}
                    </span>
                  </>
                ) : (
                  option.label
                )}
              </span>
            </div>

            {/* Status badges */}
            {asset.source === "eodhd" && !asset.isExistingAsset && (
              <span className="text-xs px-1.5 py-0.5 bg-orange-100 text-orange-700 rounded-md dark:bg-orange-900/20 dark:text-orange-300">
                Nou
              </span>
            )}
            {asset.source === "eodhd" && asset.isExistingAsset && (
              <span className="text-xs px-1.5 py-0.5 bg-green-100 text-green-700 rounded-md dark:bg-green-900/20 dark:text-green-300">
                Existent
              </span>
            )}
          </div>

          {/* Company name and additional info */}
          <div className="text-xs text-muted-foreground dark:text-white group-hover:text-accent-foreground/80 truncate">
            <span className="font-medium">{asset.name}</span>
            {asset.source === "eodhd" &&
              asset.country &&
              asset.country !== "Unknown" && (
                <span className="ml-1">• {asset.country}</span>
              )}
            {asset.source === "eodhd" && asset.type && (
              <span className="ml-1">• {asset.type}</span>
            )}
            {asset.source === "eodhd" && asset.currency && (
              <span className="ml-1">• {asset.currency}</span>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Custom selected value renderer
  const renderSelected = (option: ComboboxOption | null) => {
    if (!option) return null;

    // Find asset by matching the unique value (displayTicker or ticker)
    const asset = assets.find((a) => {
      const assetValue =
        a.source === "eodhd" && a.displayTicker ? a.displayTicker : a.ticker;
      return assetValue === option.value;
    });
    if (!asset) {
      // If we don't have asset data (e.g., manually entered ticker), show just the ticker
      return (
        <div className="flex items-center space-x-2">
          <div className="flex h-5 w-5 items-center justify-center rounded-sm bg-muted">
            <TrendingUp className="h-3 w-3 text-muted-foreground" />
          </div>
          <span className="font-medium">{option.value}</span>
        </div>
      );
    }

    return (
      <div className="flex items-center space-x-2">
        {asset.logo_url ? (
          <img
            src={asset.logo_url}
            alt={`${asset.company} logo`}
            className="h-5 w-5 rounded-sm object-contain"
            onError={(e) => {
              e.currentTarget.style.display = "none";
              const fallback = e.currentTarget
                .nextElementSibling as HTMLElement;
              if (fallback) fallback.style.display = "flex";
            }}
          />
        ) : null}
        <div
          className={cn(
            "flex h-5 w-5 items-center justify-center rounded-sm bg-muted",
            asset.logo_url ? "hidden" : "flex"
          )}
        >
          <TrendingUp className="h-3 w-3 text-muted-foreground" />
        </div>
        {/* Ticker display with exchange highlighting */}
        <div className="flex items-center space-x-1">
          <span className="font-medium">
            {asset.source === "eodhd" && asset.displayTicker ? (
              <>
                <span>{asset.ticker}</span>
                <span className="text-blue-600 dark:text-blue-400">
                  .{asset.exchange}
                </span>
              </>
            ) : (
              asset.ticker
            )}
          </span>
          {/* Exchange badge for selected value */}
          {asset.source === "eodhd" && asset.exchange && (
            <span className="text-xs px-1 py-0.5 bg-blue-50 text-blue-700 rounded text-[10px] dark:bg-blue-900/20 dark:text-blue-300">
              {asset.exchange}
            </span>
          )}
        </div>
        <span className="text-muted-foreground">•</span>
        <span className="text-sm text-muted-foreground truncate">
          {asset.name}
        </span>
      </div>
    );
  };

  // Determine empty message
  const emptyMessage = React.useMemo(() => {
    if (isCreatingAsset) {
      return "Se creează activul...";
    }
    if (creationError) {
      return `Eroare la creare: ${creationError}`;
    }
    if (shouldUseSearch && searchError) {
      return `Eroare: ${searchError}`;
    }
    if (searchTerm.length < 1) {
      return providedAssets
        ? "Introdu cel puțin 1 caracter pentru a căuta în activele disponibile"
        : "Introdu cel puțin 1 caracter pentru a căuta";
    }
    if (providedAssets && providedAssets.length === 0) {
      return "Nu există active disponibile";
    }
    if (
      providedAssets &&
      searchTerm.length >= 1 &&
      filteredProvidedAssets.length === 0
    ) {
      return `Nu s-au găsit active pentru "${searchTerm}" în activele disponibile`;
    }
    if (isValidTickerFormat(searchTerm)) {
      return `Nu s-au găsit active pentru "${searchTerm}".`;
    }
    return allowCustomValue
      ? "Nu s-au găsit active. Poți introduce manual simbolul."
      : "Nu s-au găsit active.";
  }, [
    shouldUseSearch,
    searchError,
    searchTerm,
    isCreatingAsset,
    creationError,
    providedAssets,
    filteredProvidedAssets,
    allowCustomValue,
  ]);

  return (
    <div className="space-y-2">
      <Combobox
        options={options}
        value={value}
        onValueChange={handleValueChange}
        onSearchChange={handleSearchChange}
        searchValue={searchTerm}
        placeholder={placeholder}
        searchPlaceholder="Caută după simbol, nume sau companie..."
        emptyMessage={emptyMessage}
        disabled={disabled || isCreatingAsset}
        loading={shouldUseSearch ? loading : false} // Only show loading when using search
        loadingMessage="Se caută..."
        error={
          error || (shouldUseSearch ? !!searchError : false) || !!creationError
        }
        className={className}
        allowCustomValue={allowCustomValue}
        renderOption={renderOption}
        renderSelected={renderSelected}
      />

      {/* External loading indicator for asset creation */}
      {isCreatingAsset && (
        <div className="flex items-center space-x-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <LoadingSpinner size="sm" variant="default" />
          <span className="text-sm text-blue-700 dark:text-blue-300 font-medium">
            Se creează activul...
          </span>
        </div>
      )}
    </div>
  );
}
