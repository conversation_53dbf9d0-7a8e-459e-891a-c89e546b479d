"use client";

import { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertTriangle, RefreshCw, Home, BarChart3 } from "lucide-react";
import Link from "next/link";

interface DashboardErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function DashboardError({ error, reset }: DashboardErrorProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Dashboard error:", error);
  }, [error]);

  // Determine error type and provide appropriate messaging
  const getErrorInfo = () => {
    const message = error.message.toLowerCase();
    
    if (message.includes("autentificat") || message.includes("unauthorized")) {
      return {
        title: "Eroare de autentificare",
        description: "Sesiunea dvs. a expirat. Vă rugăm să vă autentificați din nou.",
        showRetry: false,
        showLogin: true,
      };
    }
    
    if (message.includes("portofoliu") || message.includes("portfolio")) {
      return {
        title: "Eroare la încărcarea portofoliilor",
        description: "Nu s-au putut încărca datele portofoliilor. Verificați conexiunea la internet și încercați din nou.",
        showRetry: true,
        showLogin: false,
      };
    }
    
    if (message.includes("compoziție") || message.includes("composition")) {
      return {
        title: "Eroare la încărcarea analizei",
        description: "Nu s-au putut încărca datele de analiză ale portofoliului. Încercați să reîmprospătați pagina.",
        showRetry: true,
        showLogin: false,
      };
    }
    
    // Generic error
    return {
      title: "A apărut o eroare neașteptată",
      description: "Dashboard-ul nu poate fi încărcat momentan. Vă rugăm să încercați din nou sau să contactați suportul dacă problema persistă.",
      showRetry: true,
      showLogin: false,
    };
  };

  const errorInfo = getErrorInfo();

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-red-50 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4">
            <AlertTriangle className="h-8 w-8 text-red-600 dark:text-red-400" />
          </div>
          <CardTitle className="text-xl text-red-800 dark:text-red-200">
            {errorInfo.title}
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <p className="text-center text-muted-foreground leading-relaxed">
            {errorInfo.description}
          </p>
          
          {/* Error details for development */}
          {process.env.NODE_ENV === "development" && (
            <details className="text-xs text-muted-foreground bg-muted p-3 rounded-md">
              <summary className="cursor-pointer font-medium mb-2">
                Detalii tehnice (doar în dezvoltare)
              </summary>
              <pre className="whitespace-pre-wrap break-words">
                {error.message}
                {error.digest && `\nDigest: ${error.digest}`}
              </pre>
            </details>
          )}
          
          {/* Action buttons */}
          <div className="flex flex-col gap-3">
            {errorInfo.showRetry && (
              <Button onClick={reset} className="w-full">
                <RefreshCw className="mr-2 h-4 w-4" />
                Încearcă din nou
              </Button>
            )}
            
            {errorInfo.showLogin && (
              <Button asChild className="w-full">
                <Link href="/auth/signin">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Autentifică-te
                </Link>
              </Button>
            )}
            
            <Button variant="outline" asChild className="w-full">
              <Link href="/">
                <Home className="mr-2 h-4 w-4" />
                Înapoi la pagina principală
              </Link>
            </Button>
          </div>
          
          {/* Support contact */}
          <div className="text-center pt-4 border-t">
            <p className="text-sm text-muted-foreground">
              Problema persistă?{" "}
              <Link 
                href="/contact" 
                className="text-portavio-orange hover:text-portavio-orange-hover underline"
              >
                Contactează suportul
              </Link>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
