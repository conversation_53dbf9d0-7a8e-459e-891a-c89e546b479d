import { z } from "zod";

// Date utility helpers
export const getTodayDateString = (): string => {
  const today = new Date();
  return (
    today.getFullYear() +
    "-" +
    String(today.getMonth() + 1).padStart(2, "0") +
    "-" +
    String(today.getDate()).padStart(2, "0")
  );
};

export const getYesterdayDateString = (): string => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return (
    yesterday.getFullYear() +
    "-" +
    String(yesterday.getMonth() + 1).padStart(2, "0") +
    "-" +
    String(yesterday.getDate()).padStart(2, "0")
  );
};

// Transaction Types
export const TRANSACTION_TYPES = ["BUY", "SELL"] as const;
export type TransactionType = (typeof TRANSACTION_TYPES)[number];

// Transaction Form Schema
export const transactionFormSchema = z.object({
  // Transaction type - BUY or SELL
  type: z.enum(TRANSACTION_TYPES),

  // Ticker symbol - required, trimmed, uppercase
  ticker: z
    .string()
    .min(1, "Simbolul este obligatoriu")
    .max(20, "Simbolul nu poate avea mai mult de 20 de caractere")
    .transform((val) => val.trim().toUpperCase())
    .refine((val) => val.length > 0, "Simbolul nu poate fi gol")
    .refine(
      (val) => /^[A-Z0-9.-]+$/.test(val),
      "Simbolul poate conține doar litere, cifre, puncte și liniuțe"
    ),

  // Price - accepts string input and converts to number with validation
  price: z
    .union([
      z.number(),
      z.string().transform((val) => {
        if (val === "" || val === ".") {
          throw new Error("Prețul este obligatoriu");
        }
        const num = parseFloat(val);
        if (isNaN(num)) {
          throw new Error("Prețul trebuie să fie un număr valid");
        }
        return num;
      }),
    ])
    .refine((val) => val > 0, "Prețul trebuie să fie pozitiv")
    .refine((val) => val <= 999999999, "Prețul este prea mare")
    .refine((val) => {
      // Check if the number has at most 8 decimal places
      const str = val.toString();
      const decimalIndex = str.indexOf(".");
      if (decimalIndex === -1) return true;
      return str.length - decimalIndex - 1 <= 8;
    }, "Prețul poate avea maximum 8 zecimale"),

  // Quantity - accepts string input and converts to number with validation
  quantity: z
    .union([
      z.number(),
      z.string().transform((val) => {
        if (val === "" || val === ".") {
          throw new Error("Cantitatea este obligatorie");
        }
        const num = parseFloat(val);
        if (isNaN(num)) {
          throw new Error("Cantitatea trebuie să fie un număr valid");
        }
        return num;
      }),
    ])
    .refine((val) => val > 0, "Cantitatea trebuie să fie pozitivă")
    .refine((val) => val <= 999999999, "Cantitatea este prea mare")
    .refine((val) => {
      // Check if the number has at most 8 decimal places
      const str = val.toString();
      const decimalIndex = str.indexOf(".");
      if (decimalIndex === -1) return true;
      return str.length - decimalIndex - 1 <= 8;
    }, "Cantitatea poate avea maximum 8 zecimale"),

  // Transaction date - required, cannot be in the future (YYYY-MM-DD format)
  transactionDate: z
    .string({
      required_error: "Data tranzacției este obligatorie",
      invalid_type_error: "Data nu este validă",
    })
    .min(1, "Data tranzacției este obligatorie")
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Data trebuie să fie în formatul YYYY-MM-DD")
    .refine((dateStr) => {
      // Validate that it's a valid date
      const date = new Date(dateStr + "T00:00:00.000Z"); // Parse as UTC to avoid timezone issues
      return !isNaN(date.getTime());
    }, "Data nu este validă")
    .refine((dateStr) => {
      // Check that date is not in the future
      const inputDate = new Date(dateStr + "T00:00:00.000Z"); // Parse as UTC
      const todayStr = getTodayDateString();
      const todayDate = new Date(todayStr + "T00:00:00.000Z"); // Parse as UTC
      return inputDate < todayDate;
    }, "Data tranzacției nu poate fi în viitor"),

  // Portfolio ID - required when user has multiple portfolios
  portfolioId: z
    .string()
    .min(1, "Portofoliul este obligatoriu")
    .uuid("ID-ul portofoliului nu este valid"),

  // Optional transaction fee - accepts string input and converts to number
  transaction_fee: z
    .union([
      z.number(),
      z.string().transform((val) => {
        if (val === "" || val === "." || val === undefined) {
          return undefined;
        }
        const num = parseFloat(val);
        if (isNaN(num)) {
          return undefined;
        }
        return num;
      }),
    ])
    .refine(
      (val) => val === undefined || val >= 0,
      "Comisionul trebuie să fie pozitiv"
    )
    .optional(),

  // Optional notes
  notes: z
    .string()
    .max(1000, "Notele nu pot avea mai mult de 1000 de caractere")
    .optional(),
});

// Form data type (after validation - numbers)
export type TransactionFormData = z.infer<typeof transactionFormSchema>;

// Form input type (during user input - strings allowed)
export type TransactionFormInput = {
  type: "BUY" | "SELL";
  ticker: string;
  price: string | number;
  quantity: string | number;
  transactionDate: string;
  portfolioId: string;
  transaction_fee?: string | number;
  notes?: string;
};

// Input schema for creating transactions (before form processing)
export const transactionInputSchema = z.object({
  type: z.enum(TRANSACTION_TYPES).default("BUY"),
  ticker: z.string().min(1, "Simbolul este obligatoriu"),
  price: z.union([
    z.number(),
    z.string().transform((val) => {
      const num = parseFloat(val);
      if (isNaN(num)) throw new Error("Prețul trebuie să fie un număr");
      return num;
    }),
  ]),
  quantity: z.union([
    z.number(),
    z.string().transform((val) => {
      const num = parseFloat(val);
      if (isNaN(num)) throw new Error("Cantitatea trebuie să fie un număr");
      return num;
    }),
  ]),
  transactionDate: z.string().min(1, "Data tranzacției este obligatorie"),
  portfolioId: z.string().min(1, "Portofoliul este obligatoriu"),
  transaction_fee: z
    .union([
      z.number(),
      z.string().transform((val) => {
        if (val === "" || val === undefined) return undefined;
        const num = parseFloat(val);
        if (isNaN(num)) return undefined;
        return num;
      }),
    ])
    .optional(),
  notes: z.string().optional(),
});

export type TransactionInputData = z.infer<typeof transactionInputSchema>;

// Edit transaction schema (for updating existing transactions)
export const editTransactionFormSchema = z.object({
  // Transaction type - can be changed in edit mode
  type: z.enum(TRANSACTION_TYPES),

  // Ticker symbol - required, trimmed, uppercase
  ticker: z
    .string()
    .min(1, "Simbolul este obligatoriu")
    .max(20, "Simbolul nu poate avea mai mult de 20 de caractere")
    .transform((val) => val.trim().toUpperCase())
    .refine((val) => val.length > 0, "Simbolul nu poate fi gol")
    .refine(
      (val) => /^[A-Z0-9.-]+$/.test(val),
      "Simbolul poate conține doar litere, cifre, puncte și liniuțe"
    ),

  // Price - accepts string input and converts to number with validation
  price: z
    .union([
      z.number(),
      z.string().transform((val) => {
        if (val === "" || val === ".") {
          throw new Error("Prețul este obligatoriu");
        }
        const num = parseFloat(val);
        if (isNaN(num)) {
          throw new Error("Prețul trebuie să fie un număr valid");
        }
        return num;
      }),
    ])
    .refine((val) => val > 0, "Prețul trebuie să fie pozitiv")
    .refine((val) => val <= 999999999, "Prețul este prea mare")
    .refine((val) => {
      // Check if the number has at most 8 decimal places
      const str = val.toString();
      const decimalIndex = str.indexOf(".");
      if (decimalIndex === -1) return true;
      return str.length - decimalIndex - 1 <= 8;
    }, "Prețul poate avea maximum 8 zecimale"),

  // Quantity - accepts string input and converts to number with validation
  quantity: z
    .union([
      z.number(),
      z.string().transform((val) => {
        if (val === "" || val === ".") {
          throw new Error("Cantitatea este obligatorie");
        }
        const num = parseFloat(val);
        if (isNaN(num)) {
          throw new Error("Cantitatea trebuie să fie un număr valid");
        }
        return num;
      }),
    ])
    .refine((val) => val > 0, "Cantitatea trebuie să fie pozitivă")
    .refine((val) => val <= 999999999, "Cantitatea este prea mare")
    .refine((val) => {
      // Check if the number has at most 8 decimal places
      const str = val.toString();
      const decimalIndex = str.indexOf(".");
      if (decimalIndex === -1) return true;
      return str.length - decimalIndex - 1 <= 8;
    }, "Cantitatea poate avea maximum 8 zecimale"),

  // Transaction date - required, cannot be in the future (YYYY-MM-DD format)
  transactionDate: z
    .string({
      required_error: "Data tranzacției este obligatorie",
      invalid_type_error: "Data nu este validă",
    })
    .min(1, "Data tranzacției este obligatorie")
    .refine((dateStr) => {
      return /^\d{4}-\d{2}-\d{2}$/.test(dateStr);
    }, "Data trebuie să fie în formatul YYYY-MM-DD")
    .refine((dateStr) => {
      const date = new Date(dateStr + "T00:00:00.000Z");
      return !isNaN(date.getTime());
    }, "Data nu este validă")
    .refine((dateStr) => {
      const date = new Date(dateStr + "T00:00:00.000Z");
      const todayStr = getTodayDateString();
      const todayDate = new Date(todayStr + "T00:00:00.000Z");
      return date <= todayDate;
    }, "Data tranzacției nu poate fi în viitor"),

  // Optional transaction fee - accepts string input and converts to number
  transaction_fee: z
    .union([
      z.number(),
      z.string().transform((val) => {
        if (val === "" || val === "." || val === undefined) {
          return undefined;
        }
        const num = parseFloat(val);
        if (isNaN(num)) {
          return undefined;
        }
        return num;
      }),
    ])
    .refine(
      (val) => val === undefined || val >= 0,
      "Comisionul trebuie să fie pozitiv"
    )
    .optional(),

  // Optional notes
  notes: z
    .string()
    .max(1000, "Notele nu pot avea mai mult de 1000 de caractere")
    .optional(),
});

export type EditTransactionFormData = z.infer<typeof editTransactionFormSchema>;

// Edit form input type (during user input - strings allowed)
export type EditTransactionFormInput = {
  type: "BUY" | "SELL";
  ticker: string;
  price: string | number;
  quantity: string | number;
  transactionDate: string;
  transaction_fee?: string | number;
  notes?: string;
};

// Update transaction input schema (for API calls)
export const updateTransactionInputSchema = z.object({
  type: z.enum(TRANSACTION_TYPES).optional(),
  ticker: z.string().min(1, "Simbolul este obligatoriu").optional(),
  price: z
    .union([
      z.number(),
      z.string().transform((val) => {
        const num = parseFloat(val);
        if (isNaN(num)) throw new Error("Prețul trebuie să fie un număr");
        return num;
      }),
    ])
    .optional(),
  quantity: z
    .union([
      z.number(),
      z.string().transform((val) => {
        const num = parseFloat(val);
        if (isNaN(num)) throw new Error("Cantitatea trebuie să fie un număr");
        return num;
      }),
    ])
    .optional(),
  transactionDate: z
    .string()
    .min(1, "Data tranzacției este obligatorie")
    .optional(),
  transaction_fee: z
    .union([
      z.number(),
      z.string().transform((val) => {
        if (val === "" || val === undefined) return undefined;
        const num = parseFloat(val);
        if (isNaN(num)) return undefined;
        return num;
      }),
    ])
    .optional(),
  notes: z.string().optional(),
});

export type UpdateTransactionInputData = z.infer<
  typeof updateTransactionInputSchema
>;

// Portfolio selection schema
export const portfolioSelectionSchema = z.object({
  portfolioId: z.string().uuid("ID-ul portofoliului nu este valid"),
});

export type PortfolioSelectionData = z.infer<typeof portfolioSelectionSchema>;

// Validation helpers
export const validateTicker = (ticker: string): boolean => {
  return /^[A-Z0-9.-]+$/.test(ticker.trim().toUpperCase());
};

export const validateTransactionDate = (dateStr: string): boolean => {
  // Check format
  if (!/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) return false;

  // Check if it's a valid date
  const date = new Date(dateStr + "T00:00:00.000Z");
  if (isNaN(date.getTime())) return false;

  // Check if it's not in the future
  const todayStr = getTodayDateString();
  const todayDate = new Date(todayStr + "T00:00:00.000Z");

  return date <= todayDate;
};

export const validateQuantity = (quantity: number): boolean => {
  if (quantity <= 0) return false;
  if (quantity > 999999999) return false;

  // Check decimal places
  const str = quantity.toString();
  const decimalIndex = str.indexOf(".");
  if (decimalIndex !== -1 && str.length - decimalIndex - 1 > 8) {
    return false;
  }

  return true;
};

// Format helpers
export const formatPrice = (price: number): string => {
  // Format with up to 8 decimal places, removing trailing zeros
  return price.toFixed(8).replace(/\.?0+$/, "");
};

export const formatQuantity = (quantity: number): string => {
  // Format with up to 8 decimal places, removing trailing zeros
  return quantity.toFixed(8).replace(/\.?0+$/, "");
};

export const formatTransactionDate = (dateStr: string): string => {
  const date = new Date(dateStr + "T00:00:00.000Z");
  return date.toLocaleDateString("ro-RO");
};

// Default values
export const getDefaultTransactionFormData = (
  portfolioId?: string
): Partial<TransactionFormInput> => {
  return {
    type: "BUY" as const,
    ticker: "",
    price: "",
    quantity: "",
    transactionDate: getYesterdayDateString(),
    portfolioId: portfolioId || "",
    transaction_fee: "",
    notes: "",
  };
};

// Convert transaction data to edit form data
export const getEditTransactionFormData = (transaction: {
  ticker: string;
  price: number;
  quantity: number;
  transaction_date: string;
  transaction_type: "BUY" | "SELL";
  transaction_fee?: number;
  notes?: string;
}): EditTransactionFormInput => {
  return {
    type: transaction.transaction_type,
    ticker: transaction.ticker,
    price: transaction.price.toString(),
    quantity: transaction.quantity.toString(),
    transactionDate: transaction.transaction_date,
    transaction_fee: transaction.transaction_fee?.toString() || "",
    notes: transaction.notes || "",
  };
};

// Helper to check if transaction data has changed
export const hasTransactionChanged = (
  original: {
    ticker: string;
    price: number;
    quantity: number;
    transaction_date: string;
    transaction_type: "BUY" | "SELL";
    transaction_fee?: number;
    notes?: string;
  },
  updated: EditTransactionFormData
): boolean => {
  return (
    original.ticker !== updated.ticker ||
    original.price !== updated.price ||
    original.quantity !== updated.quantity ||
    original.transaction_date !== updated.transactionDate ||
    original.transaction_type !== updated.type ||
    (original.transaction_fee || undefined) !==
      (updated.transaction_fee || undefined) ||
    (original.notes || "") !== (updated.notes || "")
  );
};

// Get only changed fields for API update
export const getChangedTransactionFields = (
  original: {
    ticker: string;
    price: number;
    quantity: number;
    transaction_date: string;
    transaction_type: "BUY" | "SELL";
    transaction_fee?: number;
    notes?: string;
  },
  updated: EditTransactionFormData
): UpdateTransactionInputData => {
  const changes: UpdateTransactionInputData = {};

  if (original.ticker !== updated.ticker) {
    changes.ticker = updated.ticker;
  }
  if (original.price !== updated.price) {
    changes.price = updated.price;
  }
  if (original.quantity !== updated.quantity) {
    changes.quantity = updated.quantity;
  }
  if (original.transaction_date !== updated.transactionDate) {
    changes.transactionDate = updated.transactionDate;
  }
  if (original.transaction_type !== updated.type) {
    changes.type = updated.type;
  }
  if (
    (original.transaction_fee || undefined) !==
    (updated.transaction_fee || undefined)
  ) {
    changes.transaction_fee = updated.transaction_fee;
  }
  if ((original.notes || "") !== (updated.notes || "")) {
    changes.notes = updated.notes;
  }

  return changes;
};
